{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}

<style>
  .peakmotion-slider {
    padding: 60px 0;
  }

  .peakmotion-slider__header {
    text-align: center;
    margin-bottom: 40px;
  }

  .peakmotion-slider__title {
    font-family: var(--font-heading-family);
    font-size: 3rem;
    color: var(--color-peakmotion-navy);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
  }

  .peakmotion-slider__description {
    font-size: 1.2rem;
    color: var(--color-peakmotion-text);
    max-width: 600px;
    margin: 0 auto;
  }

  .peakmotion-slider__container {
    position: relative;
    overflow: hidden;
  }

  .peakmotion-slider__track {
    display: flex;
    gap: 20px;
    transition: transform 0.3s ease;
  }

  .peakmotion-slider__slide {
    flex: 0 0 300px;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(27, 38, 59, 0.1);
    transition: all 0.3s ease;
  }

  .peakmotion-slider__slide:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(27, 38, 59, 0.15);
  }

  .peakmotion-slider__image {
    width: 100%;
    height: 250px;
    object-fit: cover;
  }

  .peakmotion-slider__content {
    padding: 20px;
    text-align: center;
  }

  .peakmotion-slider__product-title {
    font-family: var(--font-heading-family);
    font-size: 1.4rem;
    color: var(--color-peakmotion-navy);
    margin-bottom: 10px;
    text-transform: uppercase;
  }

  .peakmotion-slider__price {
    color: var(--color-peakmotion-gold);
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: 15px;
  }

  .peakmotion-slider__button {
    background: var(--color-peakmotion-navy);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
  }

  .peakmotion-slider__button:hover {
    background: var(--color-peakmotion-gold);
    color: var(--color-peakmotion-navy);
    transform: translateY(-2px);
  }

  .peakmotion-slider__controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
  }

  .peakmotion-slider__arrow {
    background: var(--color-peakmotion-navy);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .peakmotion-slider__arrow:hover {
    background: var(--color-peakmotion-gold);
    color: var(--color-peakmotion-navy);
  }

  @media screen and (max-width: 749px) {
    .peakmotion-slider__slide {
      flex: 0 0 250px;
    }
    
    .peakmotion-slider__title {
      font-size: 2rem;
    }
  }
</style>

<div class="peakmotion-slider color-{{ section.settings.color_scheme }} gradient">
  <div class="page-width">
    {%- if section.settings.title != blank or section.settings.description != blank -%}
      <div class="peakmotion-slider__header">
        {%- if section.settings.title != blank -%}
          <h2 class="peakmotion-slider__title">{{ section.settings.title }}</h2>
        {%- endif -%}
        {%- if section.settings.description != blank -%}
          <div class="peakmotion-slider__description">{{ section.settings.description }}</div>
        {%- endif -%}
      </div>
    {%- endif -%}

    {%- if section.settings.collection != blank -%}
      <div class="peakmotion-slider__container" id="slider-{{ section.id }}">
        <div class="peakmotion-slider__track">
          {%- for product in section.settings.collection.products limit: section.settings.products_to_show -%}
            <div class="peakmotion-slider__slide">
              {%- if product.featured_media -%}
                <img 
                  src="{{ product.featured_media | image_url: width: 300 }}"
                  alt="{{ product.featured_media.alt | escape }}"
                  class="peakmotion-slider__image"
                  loading="lazy"
                >
              {%- endif -%}
              
              <div class="peakmotion-slider__content">
                <h3 class="peakmotion-slider__product-title">{{ product.title }}</h3>
                <div class="peakmotion-slider__price">
                  {%- render 'price', product: product, price_class: '' -%}
                </div>
                <a href="{{ product.url }}" class="peakmotion-slider__button">
                  {{ 'products.product.view_product' | t | default: 'Ürünü Görüntüle' }}
                </a>
              </div>
            </div>
          {%- endfor -%}
        </div>
        
        <div class="peakmotion-slider__controls">
          <button class="peakmotion-slider__arrow" onclick="slideProducts('{{ section.id }}', -1)">
            &#8249;
          </button>
          <button class="peakmotion-slider__arrow" onclick="slideProducts('{{ section.id }}', 1)">
            &#8250;
          </button>
        </div>
      </div>
    {%- endif -%}
  </div>
</div>

<script>
  function slideProducts(sectionId, direction) {
    const container = document.getElementById('slider-' + sectionId);
    const track = container.querySelector('.peakmotion-slider__track');
    const slideWidth = 320; // 300px + 20px gap
    const currentTransform = track.style.transform || 'translateX(0px)';
    const currentX = parseInt(currentTransform.replace('translateX(', '').replace('px)', '')) || 0;
    const newX = currentX + (direction * slideWidth * -1);
    
    track.style.transform = `translateX(${newX}px)`;
  }
</script>

{% schema %}
{
  "name": "PeakMotion Product Slider",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "NASIL KULLANILIR"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 12,
      "step": 1,
      "default": 6,
      "label": "Products to show"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    }
  ],
  "presets": [
    {
      "name": "PeakMotion Product Slider"
    }
  ]
}
{% endschema %}
