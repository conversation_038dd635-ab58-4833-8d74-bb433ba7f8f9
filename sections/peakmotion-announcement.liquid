{{ 'component-slider.css' | asset_url | stylesheet_tag }}

<style>
  .peakmotion-announcement {
    background: var(--color-peakmotion-navy, #1b263b);
    color: var(--color-peakmotion-white, #ffffff);
    padding: 12px 0;
    overflow: hidden;
    position: relative;
  }

  .peakmotion-announcement__container {
    display: flex;
    animation: scroll-horizontal 30s linear infinite;
    white-space: nowrap;
  }

  .peakmotion-announcement__item {
    font-size: 1.4rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0 4rem;
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
  }

  .peakmotion-announcement__icon {
    margin-right: 8px;
    font-size: 1.2em;
  }

  @keyframes scroll-horizontal {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  @media screen and (max-width: 749px) {
    .peakmotion-announcement__item {
      font-size: 1.2rem;
      padding: 0 2rem;
    }
  }
</style>

<div class="peakmotion-announcement">
  <div class="peakmotion-announcement__container">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when 'announcement' -%}
          <div class="peakmotion-announcement__item" {{ block.shopify_attributes }}>
            {%- if block.settings.icon != blank -%}
              <span class="peakmotion-announcement__icon">{{ block.settings.icon }}</span>
            {%- endif -%}
            {{ block.settings.text }}
          </div>
      {%- endcase -%}
    {%- endfor -%}
    
    {%- comment -%} Duplicate for seamless loop {%- endcomment -%}
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when 'announcement' -%}
          <div class="peakmotion-announcement__item">
            {%- if block.settings.icon != blank -%}
              <span class="peakmotion-announcement__icon">{{ block.settings.icon }}</span>
            {%- endif -%}
            {{ block.settings.text }}
          </div>
      {%- endcase -%}
    {%- endfor -%}
  </div>
</div>

{% schema %}
{
  "name": "PeakMotion Announcement",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "animation_speed",
      "min": 10,
      "max": 60,
      "step": 5,
      "default": 30,
      "unit": "s",
      "label": "Animation Speed"
    }
  ],
  "blocks": [
    {
      "type": "announcement",
      "name": "Announcement",
      "settings": [
        {
          "type": "text",
          "id": "icon",
          "label": "Icon (emoji)",
          "default": "🛡️"
        },
        {
          "type": "text",
          "id": "text",
          "label": "Announcement Text",
          "default": "30 GÜN ÜCRETSİZ İADE VE DEĞİŞİM İMKANI"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "PeakMotion Announcement",
      "blocks": [
        {
          "type": "announcement",
          "settings": {
            "icon": "🛡️",
            "text": "30 GÜN ÜCRETSİZ İADE VE DEĞİŞİM İMKANI"
          }
        },
        {
          "type": "announcement",
          "settings": {
            "icon": "📣",
            "text": "BU HAFTA GEÇERLİ %33 LANSMAN İNDİRİMİNİ KAÇIRMAYIN!"
          }
        },
        {
          "type": "announcement",
          "settings": {
            "icon": "🔥",
            "text": "SINIRLI SÜREYE ÖZEL İKİNCİ ÜRÜNDE 200 TL İNDİRİM"
          }
        }
      ]
    }
  ]
}
{% endschema %}
