/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "general": {
    "password_page": {
      "login_form_heading": "Parolayı kullanarak mağazaya girin:",
      "login_password_button": "Parola kullanarak gir",
      "login_form_password_label": "Parola",
      "login_form_password_placeholder": "Parolanız",
      "login_form_error": "Yanlış parola!",
      "login_form_submit": "Gir",
      "admin_link_html": "<PERSON><PERSON><PERSON><PERSON> sahi<PERSON> misiniz? <a href=\"/admin\" class=\"link underlined-link\">B<PERSON>dan oturum açın</a>",
      "powered_by_shopify_html": "Bu mağaza {{ shopify }} tarafından desteklenir"
    },
    "social": {
      "alt_text": {
        "share_on_facebook": "Facebook'ta paylaş",
        "share_on_twitter": "X'te paylaş",
        "share_on_pinterest": "Pinterest'te pin ekle"
      },
      "links": {
        "twitter": "X (Twitter)",
        "facebook": "Facebook",
        "pinterest": "Pinterest",
        "instagram": "Instagram",
        "tumblr": "Tumblr",
        "snapchat": "Snapchat",
        "youtube": "YouTube",
        "vimeo": "Vimeo",
        "tiktok": "TikTok"
      }
    },
    "continue_shopping": "Alışverişe devam et",
    "pagination": {
      "label": "Sayfalara ayırma",
      "page": "Sayfa {{ number }}",
      "next": "Sonraki sayfa",
      "previous": "Önceki sayfa"
    },
    "search": {
      "search": "Ara",
      "reset": "Arama terimini temizle"
    },
    "cart": {
      "view": "Sepeti görüntüle ({{ count }})",
      "item_added": "Ürün sepetinize eklendi",
      "view_empty_cart": "Sepeti görüntüle"
    },
    "share": {
      "copy_to_clipboard": "Bağlantıyı kopyala",
      "share_url": "Bağlantı",
      "success_message": "Bağlantı panoya kopyalandı",
      "close": "Paylaşımı kapat"
    },
    "slider": {
      "of": "/",
      "next_slide": "Sağa kaydır",
      "previous_slide": "Sola kaydır",
      "name": "Kaydırıcı"
    }
  },
  "newsletter": {
    "label": "E-posta",
    "success": "Abone olduğunuz için teşekkür ederiz",
    "button_label": "Abone ol"
  },
  "accessibility": {
    "skip_to_text": "İçeriğe atla",
    "close": "Kapat",
    "unit_price_separator": "/",
    "vendor": "Satıcı:",
    "error": "Hata",
    "refresh_page": "Bir seçim yapmanız sayfanın tamamının yenilenmesine neden olur.",
    "link_messages": {
      "new_window": "Yeni bir pencerede açılır.",
      "external": "Harici web sitesini açar."
    },
    "loading": "Yükleniyor...",
    "skip_to_product_info": "Ürün bilgisine atla",
    "total_reviews": "toplam değerlendirme",
    "star_reviews_info": "{{ rating_value }}/{{ rating_max }} yıldız",
    "collapsible_content_title": "Daraltılabilir içerik",
    "complementary_products": "Tamamlayıcı ürünler"
  },
  "blogs": {
    "article": {
      "blog": "Blog",
      "read_more_title": "Devamını okuyun: {{ title }}",
      "comments": {
        "one": "{{ count }} yorum",
        "other": "{{ count }} yorum"
      },
      "moderated": "Yorumların yayınlanabilmesi için onaylanması gerektiğini lütfen unutmayın.",
      "comment_form_title": "Yorum yapın",
      "name": "Ad",
      "email": "E-posta",
      "message": "Yorum",
      "post": "Yorumu paylaş",
      "back_to_blog": "Bloga dön",
      "share": "Bu makaleyi paylaş",
      "success": "Yorumunuz başarıyla paylaşıldı! Teşekkür ederiz.",
      "success_moderated": "Yorumunuz başarıyla paylaşıldı. Blogumuz denetlendiğinden yorumunuzu kısa bir süre sonra yayınlayacağız."
    }
  },
  "onboarding": {
    "product_title": "Örnek ürün başlığı",
    "collection_title": "Koleksiyonunuzun adı"
  },
  "products": {
    "product": {
      "add_to_cart": "SEPETE EKLE",
      "description": "Açıklama",
      "on_sale": "İndirim",
      "quantity": {
        "label": "Adet",
        "input_label": "{{ product }} için adet",
        "increase": "{{ product }} için adedi artırın",
        "decrease": "{{ product }} için adedi azaltın",
        "minimum_of": "Minimum: {{ quantity }}",
        "maximum_of": "Maksimum: {{ quantity }}",
        "multiples_of": "Artış değeri: {{ quantity }}",
        "in_cart_html": "Sepette: <span class=\"quantity-cart\">{{ quantity }}</span>",
        "note": "Adet kurallarını görüntüle",
        "min_of": "Minimum {{ quantity }}",
        "max_of": "Maksimum {{ quantity }}",
        "in_cart_aria_label": "Adet (sepetteki: {{ quantity }})"
      },
      "price": {
        "from_price_html": "Başlangıç fiyatı: {{ price }}",
        "regular_price": "Normal fiyat",
        "sale_price": "İndirimli fiyat",
        "unit_price": "Birim fiyat"
      },
      "share": "Bu ürünü paylaş",
      "sold_out": "Tükendi",
      "unavailable": "Kullanım dışı",
      "vendor": "Satıcı",
      "video_exit_message": "{{ title }} aynı pencerede tam ekran video açar.",
      "xr_button": "Kendi alanınızda görüntüleyin",
      "xr_button_label": "Alanınızda görüntüleyin; ürün, artırılmış gerçeklik penceresinde yüklenir",
      "pickup_availability": {
        "view_store_info": "Mağaza bilgilerini görüntüleyin",
        "check_other_stores": "Diğer mağazalardaki stok durumunu kontrol edin",
        "pick_up_available": "Teslim alım kullanılabilir",
        "pick_up_available_at_html": "Teslim alım <span class=\"color-foreground\">{{ location_name }}</span> konumunda kullanılabilir",
        "pick_up_unavailable_at_html": "Teslim alım <span class=\"color-foreground\">{{ location_name }}</span> konumunda şu anda kullanılamıyor",
        "unavailable": "Teslim alım stok durumu yüklenemedi",
        "refresh": "Yenile"
      },
      "media": {
        "open_media": "Medya {{ index }} modda oynatın",
        "play_model": "3B Görüntüleyici'yi Oynat",
        "play_video": "Videoyu oynat",
        "gallery_viewer": "Galeri Görüntüleyici",
        "load_image": "Görsel {{ index }} galeri görüntüleyicide yükleyin",
        "load_model": "3B Model {{ index }} galeri görüntüleyicide yükleyin",
        "load_video": "Video {{ index }} galeri görüntüleyicide oynatın",
        "image_available": "Görsel {{ index }} artık galeri görüntüleyicide kullanılabilir"
      },
      "nested_label": "{{ parent_title }} için {{ title }}",
      "view_full_details": "Tüm ayrıntıları görüntüle",
      "view_product": "Ürünü Görüntüle",
      "view_details": "Detayları Görüntüle",
      "shipping_policy_html": "<a href=\"{{ link }}\">Kargo</a>, ödeme sayfasında hesaplanır.",
      "choose_options": "Seçenekleri belirle",
      "choose_product_options": "{{ product_name }} için seçenekleri belirle",
      "value_unavailable": "{{ option_value }} - Kullanılamıyor",
      "variant_sold_out_or_unavailable": "Varyasyon tükendi veya kullanılamıyor",
      "inventory_in_stock": "Stokta",
      "inventory_in_stock_show_count": "Stokta {{ quantity }} adet mevcut",
      "inventory_low_stock": "Stok düzeyi düşük",
      "inventory_low_stock_show_count": "Stok düzeyi düşük: {{ quantity }} adet kaldı",
      "inventory_out_of_stock": "Stokta yok",
      "inventory_out_of_stock_continue_selling": "Stokta",
      "sku": "SKU",
      "volume_pricing": {
        "title": "Toplu Alım Bazlı Fiyatlandırma",
        "note": "Toplu alım bazlı fiyatlandırma kullanılabilir",
        "minimum": "{{ quantity }}+",
        "price_range": "{{ minimum }} - {{ maximum }}",
        "price_at_each_html": "{{ price }}/adet"
      },
      "product_variants": "Ürün varyasyonları",
      "taxes_included": "Vergiler dahil.",
      "duties_included": "Gümrük vergileri dahil.",
      "duties_and_taxes_included": "Vergiler ve gümrük vergileri dahil."
    },
    "modal": {
      "label": "Medya galerisi"
    },
    "facets": {
      "apply": "Uygula",
      "clear": "Temizle",
      "clear_all": "Tümünü kaldır",
      "from": "En düşük",
      "filter_and_sort": "Filtrele ve sırala",
      "filter_by_label": "Filtre:",
      "filter_button": "Filtrele",
      "filters_selected": {
        "one": "{{ count }} seçildi",
        "other": "{{ count }} seçildi"
      },
      "max_price": "En yüksek fiyat: {{ price }}",
      "product_count": {
        "one": "{{ count }}/{{ product_count }} ürün",
        "other": "{{ count }}/{{ product_count }} ürün"
      },
      "product_count_simple": {
        "one": "{{ count }} ürün",
        "other": "{{ count }} ürün"
      },
      "reset": "Sıfırla",
      "sort_button": "Sırala",
      "sort_by_label": "Sıralama ölçütü:",
      "to": "En yüksek",
      "clear_filter": "Filtreyi kaldır",
      "filter_selected_accessibility": "{{ type }} ({{ count }} filtre seçildi)",
      "show_more": "Daha fazla göster",
      "show_less": "Daha az göster",
      "filter_and_operator_subtitle": "Tümünü eşleştir"
    }
  },
  "templates": {
    "search": {
      "no_results": "\"{{ terms }}\" için sonuç bulunamadı. Yazım hatası olmadığını doğrulayın veya farklı bir kelime ya da ifade kullanın.",
      "results_with_count": {
        "one": "{{ count }} sonuç",
        "other": "{{ count }} sonuç"
      },
      "title": "Arama sonuçları",
      "page": "Sayfa",
      "products": "Ürünler",
      "search_for": "\"{{ terms }}\" için arama yap",
      "results_with_count_and_term": {
        "one": "\"{{ terms }}\" için {{ count }} sonuç bulundu",
        "other": "\"{{ terms }}\" için {{ count }} sonuç bulundu"
      },
      "results_pages_with_count": {
        "one": "{{ count }} sayfa",
        "other": "{{ count }} sayfa"
      },
      "results_products_with_count": {
        "one": "{{ count }} ürün",
        "other": "{{ count }} ürün"
      },
      "suggestions": "Öneriler",
      "pages": "Sayfalar",
      "results_suggestions_with_count": {
        "one": "{{ count }} öneri",
        "other": "{{ count }} öneri"
      }
    },
    "cart": {
      "cart": "Sepet"
    },
    "contact": {
      "form": {
        "name": "Ad",
        "email": "E-posta",
        "phone": "Telefon numarası",
        "comment": "Yorum",
        "send": "Gönder",
        "post_success": "Bizimle iletişime geçtiğiniz için teşekkür ederiz. Mümkün olan en kısa sürede size dönüş yapacağız.",
        "error_heading": "Lütfen aşağıdakileri düzenleyin:",
        "title": "İletişim formu"
      }
    },
    "404": {
      "title": "Sayfa bulunamadı",
      "subtext": "404"
    }
  },
  "sections": {
    "header": {
      "announcement": "Duyuru",
      "menu": "Menü",
      "cart_count": {
        "one": "{{ count }} ürün",
        "other": "{{ count }} ürün"
      }
    },
    "cart": {
      "title": "Sepetiniz",
      "caption": "Sepet ürünleri",
      "remove_title": "{{ title }} kanalını kaldır",
      "note": "Siparişe özel talimatlar",
      "checkout": "Ödeme",
      "empty": "Sepetiniz boş",
      "cart_error": "Sepetiniz güncellenirken bir hata oluştu. Lütfen tekrar deneyin.",
      "cart_quantity_error_html": "Sepetinize bu üründen yalnızca {{ quantity }} adet ekleyebilirsiniz.",
      "headings": {
        "product": "Ürün",
        "price": "Fiyat",
        "total": "Toplam",
        "quantity": "Adet",
        "image": "Ürün görseli"
      },
      "update": "Güncelle",
      "login": {
        "title": "Hesabınız var mı?",
        "paragraph_html": "Daha hızlı ödeme yapmak için <a href=\"{{ link }}\" class=\"link underlined-link\">oturum açın</a>."
      },
      "estimated_total": "Tahmini toplam",
      "new_estimated_total": "Yeni tahmini toplam",
      "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Vergiler ve gümrük vergileri dahil. İndirimler ve <a href=\"{{ link }}\">kargo</a>, ödeme sırasında hesaplanır.",
      "duties_and_taxes_included_shipping_at_checkout_without_policy": "Vergiler ve gümrük vergileri dahil. İndirimler ve kargo, ödeme sırasında hesaplanır.",
      "taxes_included_shipping_at_checkout_with_policy_html": "Vergiler dahil. İndirimler ve <a href=\"{{ link }}\">kargo</a>, ödeme sırasında hesaplanır.",
      "taxes_included_shipping_at_checkout_without_policy": "Vergiler dahil. İndirimler ve kargo, ödeme sırasında hesaplanır.",
      "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Gümrük vergileri dahil. Vergiler, indirimler ve <a href=\"{{ link }}\">kargo</a>, ödeme sayfasında hesaplanır.",
      "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Gümrük vergileri dahil. Vergiler, indirimler ve kargo, ödeme sayfasında hesaplanır.",
      "taxes_at_checkout_shipping_at_checkout_with_policy_html": "Vergiler, indirimler ve <a href=\"{{ link }}\">kargo</a>, ödeme sayfasında hesaplanır.",
      "taxes_at_checkout_shipping_at_checkout_without_policy": "Vergiler, indirimler ve kargo, ödeme sayfasında hesaplanır."
    },
    "footer": {
      "payment": "Ödeme yöntemleri"
    },
    "featured_blog": {
      "view_all": "Tümünü görüntüle",
      "onboarding_title": "Blog gönderisi",
      "onboarding_content": "Müşterilerinize blog gönderinizin özetini gösterin"
    },
    "featured_collection": {
      "view_all": "Tümünü görüntüle",
      "view_all_label": "{{ collection_name }} koleksiyonundaki tüm ürünleri görüntüle"
    },
    "collection_list": {
      "view_all": "Tümünü görüntüle"
    },
    "collection_template": {
      "title": "Koleksiyon",
      "empty": "Ürün bulunamadı",
      "use_fewer_filters_html": "Daha az filtre kullan veya <a class=\"{{ class }}\" href=\"{{ link }}\">tümünü kaldır</a>"
    },
    "video": {
      "load_video": "Videoyu yükle: {{ description }}"
    },
    "slideshow": {
      "load_slide": "Slaytı yükle",
      "previous_slideshow": "Önceki slayt",
      "next_slideshow": "Sonraki slayt",
      "pause_slideshow": "Slayt gösterisini duraklat",
      "play_slideshow": "Slayt gösterisini oynat",
      "carousel": "Döngü",
      "slide": "Slayt"
    },
    "page": {
      "title": "Sayfa başlığı"
    },
    "announcements": {
      "previous_announcement": "Önceki duyuru",
      "next_announcement": "Sonraki duyuru",
      "carousel": "Carousel",
      "announcement": "Duyuru",
      "announcement_bar": "Duyuru çubuğu"
    },
    "quick_order_list": {
      "product_total": "Ürün alt toplamı",
      "view_cart": "Sepeti görüntüle",
      "each": "{{ money }}/adet",
      "product": "Ürün",
      "variant": "Varyasyon",
      "variant_total": "Varyasyon toplamı",
      "items_added": {
        "one": "{{ quantity }} ürün eklendi",
        "other": "{{ quantity }} ürün eklendi"
      },
      "items_removed": {
        "one": "{{ quantity }} ürün kaldırıldı",
        "other": "{{ quantity }} ürün kaldırıldı"
      },
      "product_variants": "Ürün varyasyonları",
      "total_items": "Toplam ürün sayısı",
      "remove_all_items_confirmation": "{{ quantity }} ürünün tamamı sepetten kaldırılsın mı?",
      "remove_all": "Tümünü kaldır",
      "cancel": "İptal",
      "remove_all_single_item_confirmation": "1 ürün sepetten kaldırılsın mı?",
      "min_error": "Bu ürün için minimum değer: {{ min }}",
      "max_error": "Bu ürün için maksimum değer: {{ max }}",
      "step_error": "Bu öğeyi yalnızca {{ step }} değerinde artışlarla ekleyebilirsiniz"
    }
  },
  "localization": {
    "country_label": "Ülke/bölge",
    "language_label": "Dil",
    "update_language": "Dili güncelle",
    "update_country": "Ülke/bölge bilgisini güncelle",
    "search": "Ara",
    "popular_countries_regions": "Popüler ülkeler/bölgeler",
    "country_results_count": "{{ count }} ülke/bölge bulundu"
  },
  "customer": {
    "account": {
      "title": "Hesap",
      "details": "Hesap bilgileri",
      "view_addresses": "Adresi görüntüle",
      "return": "Hesap bilgilerine geri dön"
    },
    "account_fallback": "Hesap",
    "activate_account": {
      "title": "Hesabı etkinleştirin",
      "subtext": "Hesabınızı etkinleştirmek için parolanızı oluşturun.",
      "password": "Parola",
      "password_confirm": "Parolayı doğrula",
      "submit": "Hesabı etkinleştir",
      "cancel": "Daveti reddet"
    },
    "addresses": {
      "title": "Adresler",
      "default": "Varsayılan",
      "add_new": "Yeni adres ekle",
      "edit_address": "Adresi düzenle",
      "first_name": "Ad",
      "last_name": "Soyadı",
      "company": "Şirket",
      "address1": "Adres 1",
      "address2": "Adres 2",
      "city": "Şehir",
      "country": "Ülke/bölge",
      "province": "İl",
      "zip": "Posta kodu",
      "phone": "Telefon",
      "set_default": "Varsayılan adres olarak ayarla",
      "add": "Adres ekle",
      "update": "Adresi güncelle",
      "cancel": "İptal Et",
      "edit": "Düzenleyin",
      "delete": "Sil",
      "delete_confirm": "Bu adresi silmek istediğinizden emin misiniz?"
    },
    "log_in": "Oturum aç",
    "log_out": "Oturumu kapat",
    "login_page": {
      "cancel": "İptal Et",
      "create_account": "Hesap oluştur",
      "email": "E-posta",
      "forgot_password": "Parolanızı mı unuttunuz?",
      "guest_continue": "Devam",
      "guest_title": "Misafir olarak devam edin",
      "password": "Parola",
      "title": "Oturum aç",
      "sign_in": "Giriş yapın",
      "submit": "Gönder",
      "alternate_provider_separator": "veya"
    },
    "orders": {
      "title": "Sipariş geçmişi",
      "order_number": "Sipariş",
      "order_number_link": "Sipariş numarası {{ number }}",
      "date": "Tarih",
      "payment_status": "Ödeme durumu",
      "fulfillment_status": "Gönderim durumu",
      "total": "Toplam",
      "none": "Henüz sipariş vermediniz."
    },
    "recover_password": {
      "title": "Parolanızı sıfırlayın",
      "subtext": "Parolanızı sıfırlamanız için size bir e-posta göndereceğiz",
      "success": "Size parolanızı güncelleme bağlantısının bulunduğu bir e-posta gönderdik."
    },
    "register": {
      "title": "Hesap oluşturun",
      "first_name": "Ad",
      "last_name": "Soyadı",
      "email": "E-posta",
      "password": "Parola",
      "submit": "Oluştur"
    },
    "reset_password": {
      "title": "Hesap parolasını sıfırlayın",
      "subtext": "Yeni bir parola girin",
      "password": "Parola",
      "password_confirm": "Parolayı doğrula",
      "submit": "Parolayı sıfırla"
    },
    "order": {
      "title": "{{ name }} siparişi",
      "date_html": "Verilme Tarihi: {{ date }}",
      "cancelled_html": "Siparişin İptal Edilme Tarihi: {{ date }}",
      "cancelled_reason": "Neden: {{ reason }}",
      "billing_address": "Fatura Adresi",
      "payment_status": "Ödeme Durumu",
      "shipping_address": "Kargo Adresi",
      "fulfillment_status": "Gönderim Durumu",
      "discount": "İndirim",
      "shipping": "Kargo",
      "tax": "Vergi",
      "product": "Ürün",
      "sku": "SKU",
      "price": "Fiyat",
      "quantity": "Adet",
      "total": "Toplam",
      "fulfilled_at_html": "Gönderildi: {{ date }}",
      "track_shipment": "Kargoyu takip et",
      "tracking_url": "Takip bağlantısı",
      "tracking_company": "Kargo Şirketi",
      "tracking_number": "Takip numarası",
      "subtotal": "Alt toplam",
      "total_duties": "Gümrük vergileri",
      "total_refunded": "Para iadesi yapıldı"
    }
  },
  "gift_cards": {
    "issued": {
      "title": "İşte {{ shop }} için {{ value }} tutarındaki hediye kartı bakiyeniz!",
      "subtext": "Hediye kartınız",
      "gift_card_code": "Hediye kartı kodu",
      "shop_link": "Online mağazayı ziyaret edin",
      "add_to_apple_wallet": "Apple Wallet'a ekle",
      "qr_image_alt": "QR kodu: Hediye kartını kullanmak için tarayın",
      "copy_code": "Hediye kartı kodunu kopyala",
      "expired": "Süresi sona erdi",
      "copy_code_success": "Kod başarıyla kopyalandı",
      "how_to_use_gift_card": "Hediye kartı kodunu online olarak veya QR kodunu mağazada kullanın",
      "expiration_date": "Sona erme tarihi: {{ expires_on }}"
    }
  },
  "recipient": {
    "form": {
      "checkbox": "Bunu hediye olarak göndermek istiyorum",
      "email_label": "Alıcı e-postası",
      "email": "E-posta",
      "name_label": "Alıcı adı (isteğe bağlı)",
      "name": "Ad",
      "message_label": "Mesaj (isteğe bağlı)",
      "message": "Mesaj",
      "max_characters": "Maksimum {{ max_chars }} karakter",
      "email_label_optional_for_no_js_behavior": "Alıcı e-postası (isteğe bağlı)",
      "send_on": "YYYY-AA-GG",
      "send_on_label": "Şu tarihte gönder (isteğe bağlı)",
      "expanded": "Hediye kartı alıcısı formu genişletildi",
      "collapsed": "Hediye kartı alıcısı formu daraltıldı"
    },
    "quantity_discount": {
      "heading": "Miktar İndirimleri",
      "add_to_cart": "Sepete Ekle",
      "adding": "Ekleniyor...",
      "added": "✓ Eklendi",
      "savings": "tasarruf",
      "per_item": "Adet başı:",
      "error_message": "Ürün sepete eklenirken bir hata oluştu. Lütfen tekrar deneyin."
    },
    "special_features": {
      "countdown_timer": {
        "title": "Kampanya Bitiş Tarihi",
        "hours": "Saat",
        "minutes": "Dakika",
        "seconds": "Saniye"
      },
      "social_proof": {
        "favorites": "kişi bu ürünü favorilerine ekledi",
        "cart": "kişi bu ürünü sepetinde bekletiyor"
      },
      "ask_question": {
        "button": "Ürün Hakkında Soru Sor",
        "modal_title": "Ürün Hakkında Soru Sor",
        "name_label": "Adınız",
        "email_label": "E-posta Adresiniz",
        "question_label": "Sorunuz",
        "submit_button": "Soruyu Gönder",
        "sending": "Gönderiliyor...",
        "success_message": "Sorunuz başarıyla gönderildi! En kısa sürede size dönüş yapacağız.",
        "required_field": "Bu alan zorunludur"
      },
      "badges": {
        "advantage": "AVANTAJLI",
        "fast_shipping": "HIZLI KARGO",
        "limited_offer": "SINIRLI SAYIDA BUGÜNE ÖZEL"
      }
    }
  }
}
