{%- assign single_price = product.selected_or_first_available_variant.price -%}
{%- assign two_pack_original = single_price | times: 2 -%}
{%- assign two_pack_discounted = two_pack_original | times: 0.9 | round -%}
{%- assign three_pack_original = single_price | times: 3 -%}
{%- assign three_pack_discounted = three_pack_original | times: 0.85 | round -%}

<div class="bulk-discount-offers">
  <div class="bulk-discount-offers__header">
    <p class="bulk-discount-offers__subtitle">Taxes included. <a href="#" class="bulk-discount-offers__shipping-link">Shipping</a> calculated at checkout.</p>
    <h3 class="bulk-discount-offers__title">MİKTAR İNDİRİMLERİ</h3>
  </div>
  
  <div class="bulk-discount-offers__list">
    <div class="bulk-discount-offers__item">
      <div class="bulk-discount-offers__content">
        <h4 class="bulk-discount-offers__item-title">2'li Paket - %10 İndirim</h4>
        <div class="bulk-discount-offers__pricing">
          <span class="bulk-discount-offers__price">{{ two_pack_discounted | money }}</span>
          <span class="bulk-discount-offers__original-price">{{ two_pack_original | money }}</span>
        </div>
        <div class="bulk-discount-offers__savings">{{ two_pack_original | minus: two_pack_discounted | money }} tasarruf</div>
        <div class="bulk-discount-offers__unit-price">Adet başı: {{ two_pack_discounted | divided_by: 2 | money }}</div>
      </div>
      <button class="bulk-discount-offers__button" type="button" data-quantity="2" data-price="{{ two_pack_discounted }}">
        SEPETE EKLE
      </button>
    </div>

    <div class="bulk-discount-offers__item">
      <div class="bulk-discount-offers__content">
        <h4 class="bulk-discount-offers__item-title">3'lü Paket - %15 İndirim</h4>
        <div class="bulk-discount-offers__pricing">
          <span class="bulk-discount-offers__price">{{ three_pack_discounted | money }}</span>
          <span class="bulk-discount-offers__original-price">{{ three_pack_original | money }}</span>
        </div>
        <div class="bulk-discount-offers__savings">{{ three_pack_original | minus: three_pack_discounted | money }} tasarruf</div>
        <div class="bulk-discount-offers__unit-price">Adet başı: {{ three_pack_discounted | divided_by: 3 | money }}</div>
      </div>
      <button class="bulk-discount-offers__button" type="button" data-quantity="3" data-price="{{ three_pack_discounted }}">
        SEPETE EKLE
      </button>
    </div>
  </div>
</div>

<script>
// BULK DISCOUNT OFFERS DISABLED - Using quantity-discount-selector instead
console.log('⚠️ Bulk discount offers script disabled to prevent conflicts with quantity-discount-selector');
</script>
