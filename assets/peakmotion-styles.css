/* PeakMotion Custom Styles */

/* Color Variables - PeakMotion Theme */
:root {
  --color-peakmotion-navy: #1b263b;
  --color-peakmotion-gold: #cba135;
  --color-peakmotion-white: #ffffff;
  --color-peakmotion-light-gray: #f3f3f3;
  --color-peakmotion-text: #1b263b;
  
  /* Override default colors */
  --color-base-text: 27, 38, 59;
  --color-shadow: 27, 38, 59;
  --color-base-background-1: 255, 255, 255;
  --color-base-background-2: 243, 243, 243;
  --color-base-solid-button-labels: 255, 255, 255;
  --color-base-outline-button-labels: 203, 161, 53;
  --color-base-accent-1: 27, 38, 59;
  --color-base-accent-2: 203, 161, 53;
  
  /* Typography */
  --font-body-family: 'Lato', sans-serif;
  --font-heading-family: 'Bebas Neue', sans-serif;
  --font-body-scale: 1.0;
  --font-heading-scale: 1.3;
}

/* Typography Enhancements */
body {
  font-family: var(--font-body-family);
  color: var(--color-peakmotion-text);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: var(--font-heading-family);
  font-weight: 400;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

/* Header Styles */
.header {
  background: var(--color-peakmotion-white);
  border-bottom: 1px solid rgba(27, 38, 59, 0.1);
  box-shadow: 0 2px 10px rgba(27, 38, 59, 0.1);
}

.header__heading-logo {
  max-width: 130px;
}

/* Navigation Styles */
.header__menu-item {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--color-peakmotion-navy);
  transition: color 0.3s ease;
}

.header__menu-item:hover {
  color: var(--color-peakmotion-gold);
}

/* Button Styles */
.btn,
.button,
.shopify-payment-button__button--unbranded {
  background: var(--color-peakmotion-navy);
  color: var(--color-peakmotion-white);
  border: 2px solid var(--color-peakmotion-navy);
  border-radius: 6px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  padding: 12px 24px;
}

.btn:hover,
.button:hover {
  background: var(--color-peakmotion-gold);
  border-color: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(203, 161, 53, 0.3);
}

.btn--secondary,
.button--secondary {
  background: transparent;
  color: var(--color-peakmotion-navy);
  border: 2px solid var(--color-peakmotion-navy);
}

.btn--secondary:hover,
.button--secondary:hover {
  background: var(--color-peakmotion-navy);
  color: var(--color-peakmotion-white);
}

/* Product Card Styles */
.card-wrapper {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(27, 38, 59, 0.1);
  transition: all 0.3s ease;
  background: white;
}

.card-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(27, 38, 59, 0.15);
}

.card__content {
  padding: 20px;
  text-align: center;
}

.card__heading,
.card__heading a {
  font-family: var(--font-heading-family);
  color: var(--color-peakmotion-navy);
  margin-bottom: 10px;
  text-transform: uppercase;
  font-size: 1.4rem;
  letter-spacing: 0.5px;
  text-decoration: none;
}

.card__heading a:hover {
  color: var(--color-peakmotion-gold);
}

.card__media {
  border-radius: 0;
  overflow: hidden;
}

.card__media img {
  transition: transform 0.3s ease;
}

.card-wrapper:hover .card__media img {
  transform: scale(1.05);
}

.card__badge {
  background: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 0.8rem;
}

.card__information {
  padding: 15px;
}

/* Quick Add Button */
.quick-add {
  margin-top: 10px;
}

.quick-add__submit {
  background: var(--color-peakmotion-navy);
  color: white;
  border: 2px solid var(--color-peakmotion-navy);
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  width: 100%;
}

.quick-add__submit:hover {
  background: var(--color-peakmotion-gold);
  border-color: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
  transform: translateY(-2px);
}

/* Price Styles */
.price {
  color: var(--color-peakmotion-gold);
  font-weight: 700;
  font-size: 1.2em;
  margin-bottom: 10px;
}

.price--on-sale .price__regular {
  color: #999;
  text-decoration: line-through;
  margin-right: 8px;
}

.price--on-sale .price__sale {
  color: var(--color-peakmotion-gold);
}

.price__badge-sale {
  background: #e74c3c;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  margin-left: 8px;
}

.price__badge-sold-out {
  background: #95a5a6;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  margin-left: 8px;
}

/* Banner Styles */
.banner {
  position: relative;
  overflow: hidden;
}

.banner__content {
  text-align: center;
  padding: 60px 20px;
}

.banner__heading {
  font-family: var(--font-heading-family);
  font-size: 3.5rem;
  color: var(--color-peakmotion-navy);
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.banner__text {
  font-size: 1.2rem;
  color: var(--color-peakmotion-text);
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Announcement Bar */
.announcement-bar {
  background: var(--color-peakmotion-navy);
  color: var(--color-peakmotion-white);
  text-align: center;
  padding: 10px 0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Footer Styles */
.footer {
  background: var(--color-peakmotion-navy);
  color: var(--color-peakmotion-white);
  padding: 40px 0 20px;
}

.footer__heading {
  color: var(--color-peakmotion-gold);
  font-family: var(--font-heading-family);
  text-transform: uppercase;
  margin-bottom: 15px;
}

.footer__list-social .list-social__link {
  color: var(--color-peakmotion-white);
  transition: color 0.3s ease;
}

.footer__list-social .list-social__link:hover {
  color: var(--color-peakmotion-gold);
}

/* Collection Grid */
.collection-list-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  padding: 40px 0;
}

/* Responsive Design */
@media screen and (max-width: 1199px) {
  .peakmotion-slider__slide {
    flex: 0 0 280px;
  }

  .peakmotion-testimonials__slide {
    flex: 0 0 320px;
  }
}

@media screen and (max-width: 990px) {
  .header__menu-item {
    font-size: 1.4rem;
    padding: 10px 0;
  }

  .banner__heading {
    font-size: 3rem;
  }

  .peakmotion-slider__title,
  .peakmotion-testimonials__title {
    font-size: 2.5rem;
  }
}

@media screen and (max-width: 749px) {
  .banner__heading {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .banner__content {
    padding: 40px 15px;
  }

  .banner__text {
    font-size: 1.1rem;
  }

  .header__heading-logo {
    max-width: 100px;
  }

  .peakmotion-slider__slide {
    flex: 0 0 250px;
  }

  .peakmotion-testimonials__slide {
    flex: 0 0 280px;
    padding: 20px;
  }

  .peakmotion-slider__title,
  .peakmotion-testimonials__title {
    font-size: 2rem;
    margin-bottom: 15px;
  }

  .card__heading {
    font-size: 1.2rem;
  }

  .btn,
  .button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .footer__heading {
    font-size: 1.4rem;
  }

  .page-width {
    padding: 0 1rem;
  }
}

@media screen and (max-width: 480px) {
  .banner__heading {
    font-size: 2rem;
  }

  .peakmotion-slider__title,
  .peakmotion-testimonials__title {
    font-size: 1.8rem;
  }

  .peakmotion-slider__slide {
    flex: 0 0 220px;
  }

  .peakmotion-testimonials__slide {
    flex: 0 0 250px;
    padding: 15px;
  }

  .card__content {
    padding: 15px;
  }

  .btn,
  .button {
    padding: 8px 16px;
    font-size: 0.8rem;
  }
}

/* Loading Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(27, 38, 59, 0.15);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-peakmotion-light-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--color-peakmotion-gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-peakmotion-navy);
}

/* Additional PeakMotion Specific Styles */
.peakmotion-announcement__container {
  animation-duration: 75s;
}

/* Image Banner Enhancements */
.banner__box {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 40px;
  backdrop-filter: blur(10px);
}

.banner__box .banner__heading {
  color: var(--color-peakmotion-navy);
}

.banner__box .banner__text {
  color: var(--color-peakmotion-text);
}

/* Collection List Enhancements */
.collection-list .card-wrapper {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.collection-list .card-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(27, 38, 59, 0.15);
}

/* Newsletter Form */
.newsletter-form__field-wrapper {
  display: flex;
  gap: 10px;
  align-items: center;
}

.newsletter-form__field-wrapper .field__input {
  border: 2px solid var(--color-peakmotion-navy);
  border-radius: 6px;
  padding: 12px 16px;
  font-size: 1rem;
}

.newsletter-form__field-wrapper .newsletter-form__button {
  background: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
  border: 2px solid var(--color-peakmotion-gold);
  border-radius: 6px;
  padding: 12px 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletter-form__field-wrapper .newsletter-form__button:hover {
  background: var(--color-peakmotion-navy);
  border-color: var(--color-peakmotion-navy);
  color: white;
}

/* Search Form */
.search__input {
  border: 2px solid var(--color-peakmotion-navy);
  border-radius: 6px;
  padding: 12px 16px;
}

.search__button {
  background: var(--color-peakmotion-navy);
  color: white;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.search__button:hover {
  background: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
}

/* Cart Notification */
.cart-notification {
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(27, 38, 59, 0.15);
}

.cart-notification__header {
  background: var(--color-peakmotion-navy);
  color: white;
}

/* Loading States */
.loading-overlay {
  background: rgba(27, 38, 59, 0.8);
}

.loading-overlay__spinner {
  border-color: var(--color-peakmotion-gold);
  border-top-color: transparent;
}

/* Focus States */
.focus-inset:focus-visible {
  outline: 2px solid var(--color-peakmotion-gold);
  outline-offset: 2px;
}

/* Selection */
::selection {
  background: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
}

/* Product Page Styles */
.product {
  background: white;
}

.product__title {
  font-family: var(--font-heading-family);
  color: var(--color-peakmotion-navy);
  text-transform: uppercase;
  font-size: 2.5rem;
  letter-spacing: 1px;
  margin-bottom: 20px;
  text-align: center;
}

.product__text {
  color: var(--color-peakmotion-text);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

/* Product Rating */
.product__rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

.product__rating-stars {
  color: var(--color-peakmotion-gold);
  font-size: 1.2rem;
}

.product__rating-text {
  color: var(--color-peakmotion-text);
  font-weight: 500;
}

/* Product Price */
.product__price {
  text-align: center;
  margin-bottom: 30px;
}

.price--large {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-peakmotion-gold);
}

.price--on-sale .price__regular {
  color: #999;
  text-decoration: line-through;
  font-size: 1.4rem;
  margin-right: 10px;
}

.price__sale-badge {
  background: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 10px;
}

/* Product Features */
.product__features {
  background: var(--color-peakmotion-light-gray);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.product__features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product__features-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  color: var(--color-peakmotion-text);
  font-size: 1rem;
}

.product__features-icon {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

/* Quantity Discount Section */
.quantity-discount {
  background: var(--color-peakmotion-light-gray);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.quantity-discount__heading {
  font-family: var(--font-heading-family);
  color: var(--color-peakmotion-navy);
  text-transform: uppercase;
  text-align: center;
  margin-bottom: 20px;
  font-size: 1.4rem;
  letter-spacing: 0.5px;
}

.quantity-discount__option {
  border: 2px solid rgba(203, 161, 53, 0.3);
  border-radius: 12px;
  padding: 15px 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.quantity-discount__option:hover,
.quantity-discount__option.selected {
  border-color: var(--color-peakmotion-gold);
  background: rgba(203, 161, 53, 0.1);
}

.quantity-discount__option.popular {
  border-color: var(--color-peakmotion-navy);
  background: rgba(27, 38, 59, 0.05);
}

.quantity-discount__option.popular::after {
  content: "EN ÇOK SATAN";
  position: absolute;
  top: -10px;
  right: 15px;
  background: var(--color-peakmotion-navy);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.quantity-discount__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.quantity-discount__quantity {
  font-family: var(--font-heading-family);
  font-size: 1.2rem;
  color: var(--color-peakmotion-navy);
  text-transform: uppercase;
}

.quantity-discount__price {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--color-peakmotion-gold);
}

.quantity-discount__original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 1rem;
  margin-right: 10px;
}

.quantity-discount__savings {
  color: var(--color-peakmotion-text);
  font-size: 0.9rem;
  font-style: italic;
}

.quantity-discount__badge {
  background: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 10px;
}

/* Product Form */
.product-form {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(27, 38, 59, 0.1);
}

.product-form__buttons {
  margin-top: 20px;
}

.product-form__cart-submit {
  background: var(--color-peakmotion-navy);
  color: white;
  border: 2px solid var(--color-peakmotion-navy);
  border-radius: 8px;
  padding: 15px 30px;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.product-form__cart-submit:hover {
  background: var(--color-peakmotion-gold);
  border-color: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(203, 161, 53, 0.3);
}

/* Product Media */
.product__media-wrapper {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(27, 38, 59, 0.1);
}

.product__media img {
  border-radius: 12px;
}

/* Product Info Sticky */
.product__info-wrapper {
  position: sticky;
  top: 20px;
}

/* Product Media Gallery */
.product__media-list {
  gap: 10px;
}

.product__media-item {
  border-radius: 8px;
  overflow: hidden;
}

/* Product Badges */
.product__badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  z-index: 2;
}

/* Product Vendor */
.product__vendor {
  color: var(--color-peakmotion-text);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 10px;
  text-align: center;
}

/* Product Description */
.product__description {
  color: var(--color-peakmotion-text);
  line-height: 1.6;
  margin-top: 30px;
}

.product__description h3 {
  color: var(--color-peakmotion-navy);
  font-family: var(--font-heading-family);
  text-transform: uppercase;
  margin-bottom: 15px;
}

.product__description ul {
  padding-left: 20px;
}

.product__description li {
  margin-bottom: 8px;
}

/* Product Share */
.product__share {
  margin-top: 20px;
  text-align: center;
}

.share-button {
  background: var(--color-peakmotion-light-gray);
  border: 1px solid rgba(203, 161, 53, 0.3);
  color: var(--color-peakmotion-text);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.share-button:hover {
  background: var(--color-peakmotion-gold);
  border-color: var(--color-peakmotion-gold);
  color: var(--color-peakmotion-navy);
}

/* Loading Spinner */
.loading__spinner {
  color: white;
}

/* Responsive Design for Product Page */
@media screen and (max-width: 749px) {
  .product__title {
    font-size: 2rem;
    text-align: center;
  }

  .quantity-discount__option {
    padding: 12px 15px;
  }

  .quantity-discount__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .product-form__cart-submit {
    padding: 12px 20px;
    font-size: 1.1rem;
  }

  .product__features {
    padding: 15px;
  }

  .quantity-discount {
    padding: 15px;
  }

  .product__rating {
    flex-direction: column;
    gap: 5px;
  }

  .product__info-wrapper {
    position: static;
  }
}

@media screen and (max-width: 480px) {
  .product__title {
    font-size: 1.8rem;
  }

  .price--large {
    font-size: 1.6rem;
  }

  .quantity-discount__option.popular::after {
    right: 10px;
    font-size: 0.7rem;
    padding: 3px 8px;
  }
}
