// Product Special Features JavaScript

// Countdown Timer Functionality
function initCountdownTimer() {
  const timerElement = document.querySelector('.countdown-timer__display');
  if (!timerElement) return;

  const hours = parseInt(timerElement.dataset.countdownHours) || 24;
  
  // Calculate end time (current time + specified hours)
  const endTime = new Date().getTime() + (hours * 60 * 60 * 1000);
  
  // Store end time in localStorage to persist across page reloads
  const storageKey = 'countdown_end_time';
  let storedEndTime = localStorage.getItem(storageKey);
  
  if (!storedEndTime) {
    localStorage.setItem(storageKey, endTime);
    storedEndTime = endTime;
  } else {
    storedEndTime = parseInt(storedEndTime);
  }

  function updateTimer() {
    const now = new Date().getTime();
    const timeLeft = storedEndTime - now;

    if (timeLeft > 0) {
      const hours = Math.floor(timeLeft / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

      const hoursElement = document.getElementById('countdown-hours');
      const minutesElement = document.getElementById('countdown-minutes');
      const secondsElement = document.getElementById('countdown-seconds');

      if (hoursElement) hoursElement.textContent = hours.toString().padStart(2, '0');
      if (minutesElement) minutesElement.textContent = minutes.toString().padStart(2, '0');
      if (secondsElement) secondsElement.textContent = seconds.toString().padStart(2, '0');
    } else {
      // Timer expired
      const hoursElement = document.getElementById('countdown-hours');
      const minutesElement = document.getElementById('countdown-minutes');
      const secondsElement = document.getElementById('countdown-seconds');

      if (hoursElement) hoursElement.textContent = '00';
      if (minutesElement) minutesElement.textContent = '00';
      if (secondsElement) secondsElement.textContent = '00';
      
      // Reset timer for next day
      const newEndTime = new Date().getTime() + (hours * 60 * 60 * 1000);
      localStorage.setItem(storageKey, newEndTime);
      storedEndTime = newEndTime;
    }
  }

  // Update timer immediately and then every second
  updateTimer();
  setInterval(updateTimer, 1000);
}

// Ask Question Modal Functions
function openAskQuestionModal() {
  const modal = document.getElementById('ask-question-modal');
  if (modal) {
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Focus on first input
    const firstInput = modal.querySelector('input[type="text"]');
    if (firstInput) {
      setTimeout(() => firstInput.focus(), 100);
    }
  }
}

function closeAskQuestionModal() {
  const modal = document.getElementById('ask-question-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = '';
  }
}

// Handle ask question form submission
function initAskQuestionForm() {
  const form = document.getElementById('ask-question-form');
  if (!form) return;

  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(form);
    const data = {
      product_title: formData.get('product_title'),
      product_url: formData.get('product_url'),
      customer_name: formData.get('customer_name'),
      customer_email: formData.get('customer_email'),
      customer_question: formData.get('customer_question')
    };

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Gönderiliyor...';
    submitBtn.disabled = true;

    // Send email using a simple mailto link as fallback
    // In a real implementation, you would send this to your backend
    const subject = encodeURIComponent(`Ürün Sorusu: ${data.product_title}`);
    const body = encodeURIComponent(`
Müşteri Adı: ${data.customer_name}
E-posta: ${data.customer_email}
Ürün: ${data.product_title}
Ürün Linki: ${data.product_url}

Soru:
${data.customer_question}
    `);
    
    // You can replace this with your actual email sending logic
    const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    
    // Simulate sending
    setTimeout(() => {
      // Reset form
      form.reset();
      
      // Show success message
      alert('Sorunuz başarıyla gönderildi! En kısa sürede size dönüş yapacağız.');
      
      // Close modal
      closeAskQuestionModal();
      
      // Reset button
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
      
      // Open mailto link
      window.location.href = mailtoLink;
    }, 1000);
  });
}

// Close modal when clicking outside
function initModalClickOutside() {
  const modal = document.getElementById('ask-question-modal');
  if (!modal) return;

  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeAskQuestionModal();
    }
  });
}

// Close modal with Escape key
function initModalEscapeKey() {
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      const modal = document.getElementById('ask-question-modal');
      if (modal && modal.style.display === 'flex') {
        closeAskQuestionModal();
      }
    }
  });
}

// Social Proof Animation
function initSocialProofAnimation() {
  const socialProofItems = document.querySelectorAll('.social-proof__item');
  
  socialProofItems.forEach((item, index) => {
    // Add a subtle entrance animation
    item.style.opacity = '0';
    item.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
      item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
      item.style.opacity = '1';
      item.style.transform = 'translateY(0)';
    }, index * 200);
  });
}

// Initialize all features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  initCountdownTimer();
  initAskQuestionForm();
  initModalClickOutside();
  initModalEscapeKey();
  initSocialProofAnimation();
});

// Make functions globally available
window.openAskQuestionModal = openAskQuestionModal;
window.closeAskQuestionModal = closeAskQuestionModal;
