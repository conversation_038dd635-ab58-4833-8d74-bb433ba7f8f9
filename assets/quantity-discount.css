/* Quantity Discount Styles */
.quantity-discount {
  margin: 2rem 0;
  padding: 0;
  background: transparent;
}

.quantity-discount__heading {
  text-align: center;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.quantity-discount__heading::before,
.quantity-discount__heading::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30%;
  height: 2px;
  background: linear-gradient(90deg, #d4af37, #f4d03f);
}

.quantity-discount__heading::before {
  left: 0;
}

.quantity-discount__heading::after {
  right: 0;
}

.quantity-discount__options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.quantity-discount__option {
  border: 2px solid #d4af37;
  border-radius: 12px;
  padding: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fefdf8;
  position: relative;
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.1);
}

.quantity-discount__option:hover {
  border-color: #b8941f;
  box-shadow: 0 4px 16px rgba(212, 175, 55, 0.2);
  transform: translateY(-2px);
}

.quantity-discount__option.popular {
  border-color: #d4af37;
  background: linear-gradient(135deg, #fefdf8 0%, #faf8f0 100%);
  box-shadow: 0 4px 20px rgba(212, 175, 55, 0.3);
}

.quantity-discount__option.popular::before {
  content: "EN ÇOK SATAN";
  position: absolute;
  top: -12px;
  right: 20px;
  background: #2c3e50;
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 2;
}

.quantity-discount__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.quantity-discount__left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.quantity-discount__left input[type="radio"] {
  width: 24px;
  height: 24px;
  accent-color: #d4af37;
  cursor: pointer;
}

.quantity-discount__left label {
  font-weight: 700;
  color: #2c3e50;
  cursor: pointer;
  font-size: 1.1rem;
}

.quantity-discount__badge {
  background: #2c3e50;
  color: white;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.quantity-discount__right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.quantity-discount__pricing {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.quantity-discount__price {
  font-size: 1.5rem;
  font-weight: 900;
  color: #d4af37;
  letter-spacing: -0.5px;
}

.quantity-discount__original-price {
  font-size: 1rem;
  color: #7f8c8d;
  text-decoration: line-through;
  font-weight: 500;
}

.quantity-discount__savings {
  color: #d4af37;
  font-weight: 700;
  font-size: 0.9rem;
  text-align: left;
  margin-top: 0.5rem;
  padding: 0.25rem 0;
}

/* Selected state */
.quantity-discount__option:has(input:checked) {
  border-color: #b8941f;
  background: linear-gradient(135deg, #fff9e6 0%, #fef5d3 100%);
  box-shadow: 0 6px 24px rgba(212, 175, 55, 0.4);
  transform: translateY(-3px);
}

.quantity-discount__option:has(input:checked) .quantity-discount__price {
  color: #b8941f;
}

/* Product guarantee styles */
.product__guarantee {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 2px solid #28a745;
  border-radius: 8px;
  font-weight: 600;
  color: #155724;
  text-align: center;
}

.product__guarantee-icon {
  font-size: 1.2rem;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .quantity-discount {
    margin: 1rem 0;
  }
  
  .quantity-discount__heading {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
  
  .quantity-discount__heading::before,
  .quantity-discount__heading::after {
    width: 25%;
  }
  
  .quantity-discount__option {
    padding: 1rem;
  }
  
  .quantity-discount__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .quantity-discount__right {
    align-self: stretch;
  }
  
  .quantity-discount__pricing {
    align-items: flex-start;
  }
  
  .quantity-discount__price {
    font-size: 1.3rem;
  }
  
  .quantity-discount__left {
    gap: 0.5rem;
  }
  
  .quantity-discount__left label {
    font-size: 1rem;
  }
}

/* Bulk Discount Offers Styles */
.bulk-discount-offers {
  margin: 3rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.bulk-discount-offers__header {
  text-align: center;
  margin-bottom: 2rem;
}

.bulk-discount-offers__subtitle {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.bulk-discount-offers__shipping-link {
  color: #007bff;
  text-decoration: underline;
}

.bulk-discount-offers__title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0;
}

.bulk-discount-offers__list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.bulk-discount-offers__item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bulk-discount-offers__item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.bulk-discount-offers__content {
  flex: 1;
}

.bulk-discount-offers__item-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 0.75rem 0;
}

.bulk-discount-offers__pricing {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.bulk-discount-offers__price {
  font-size: 1.5rem;
  font-weight: 900;
  color: #2c3e50;
}

.bulk-discount-offers__original-price {
  font-size: 1rem;
  color: #6c757d;
  text-decoration: line-through;
}

.bulk-discount-offers__savings {
  color: #dc3545;
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.bulk-discount-offers__unit-price {
  color: #6c757d;
  font-size: 0.9rem;
}

.bulk-discount-offers__button {
  background: #2c3e50;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: bold;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bulk-discount-offers__button:hover {
  background: #1a252f;
}

/* Mobile responsive for bulk offers */
@media (max-width: 768px) {
  .bulk-discount-offers {
    margin: 2rem 0;
    padding: 1.5rem;
  }

  .bulk-discount-offers__item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .bulk-discount-offers__button {
    width: 100%;
    padding: 0.75rem;
  }

  .bulk-discount-offers__pricing {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
