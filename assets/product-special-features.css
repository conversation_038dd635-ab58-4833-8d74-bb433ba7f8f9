/* Product Special Features CSS */

/* Countdown Timer Styles */
.product__countdown-timer {
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 8px;
  color: white;
  text-align: center;
}

.countdown-timer__header {
  margin-bottom: 1rem;
}

.countdown-timer__title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.countdown-timer__display {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.countdown-timer__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.countdown-timer__number {
  font-size: 2rem;
  font-weight: bold;
  line-height: 1;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem;
  border-radius: 4px;
  min-width: 50px;
  display: block;
}

.countdown-timer__label {
  font-size: 0.8rem;
  margin-top: 0.5rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Social Proof Styles */
.product__social-proof {
  margin: 1.5rem 0;
}

.social-proof__item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
  padding: 0.8rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #28a745;
}

.social-proof__icon {
  font-size: 1.2rem;
}

.social-proof__text {
  font-size: 0.9rem;
  color: #495057;
}

.social-proof__text strong {
  color: #28a745;
  font-weight: 600;
}

/* Ask Question Button */
.product__ask-question {
  margin: 1.5rem 0;
}

.ask-question-btn {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  border: 2px solid #007bff;
  background: transparent;
  color: #007bff;
  transition: all 0.3s ease;
}

.ask-question-btn:hover {
  background: #007bff;
  color: white;
}

/* Ask Question Modal */
.ask-question-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.ask-question-modal__content {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.ask-question-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
}

.ask-question-modal__header h3 {
  margin: 0;
  font-size: 1.3rem;
  color: #333;
}

.ask-question-modal__close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ask-question-modal__close:hover {
  color: #333;
}

.ask-question-modal__body {
  padding: 1.5rem;
}

.ask-question-form .form-group {
  margin-bottom: 1.5rem;
}

.ask-question-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.ask-question-form input,
.ask-question-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.ask-question-form input:focus,
.ask-question-form textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.ask-question-form textarea {
  resize: vertical;
  min-height: 100px;
}

/* Badge Styles for Product Cards */
.advantage-badge,
.fast-shipping-badge {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.advantage-badge {
  background: #28a745;
  color: white;
}

.fast-shipping-badge {
  background: #17a2b8;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .countdown-timer__display {
    gap: 0.5rem;
  }
  
  .countdown-timer__number {
    font-size: 1.5rem;
    min-width: 40px;
    padding: 0.3rem;
  }
  
  .countdown-timer__label {
    font-size: 0.7rem;
  }
  
  .ask-question-modal {
    padding: 0.5rem;
  }
  
  .ask-question-modal__content {
    max-height: 95vh;
  }
  
  .social-proof__item {
    padding: 0.6rem;
  }
  
  .social-proof__text {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .countdown-timer__display {
    flex-wrap: wrap;
    gap: 0.3rem;
  }
  
  .countdown-timer__item {
    min-width: 50px;
  }
  
  .countdown-timer__number {
    font-size: 1.2rem;
    min-width: 35px;
  }
}
